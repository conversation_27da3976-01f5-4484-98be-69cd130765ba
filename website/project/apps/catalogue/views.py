import json
import logging
from decimal import Decimal as D
import datetime
from django.utils import timezone
import hashlib
import os

from django.core.mail import send_mail
from django.urls import reverse
from django.conf import settings
from django.contrib import messages
from django.http import HttpResponsePermanentRedirect, HttpResponse, HttpResponseRedirect, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404, render
from django.utils.translation import gettext_lazy as _
from django.views.generic import View, FormView, ListView
from django.template.response import TemplateResponse
from django.db.models import Count, Q, Prefetch, Exists, OuterRef, F
from django.core.cache import cache
from django.apps import apps

from oscar.apps.catalogue import views
from oscar.core.loading import get_model

from project.apps.catalogue.models import Product, Category, get_product_counts_by_category_path, get_extra_attribute_counts
from project.apps.basket.forms import ShippingForm
from project.apps.catalogue.models import FakeCategory
from project.apps.promotions.views import SearchMixin
from project.apps.search.forms import SearchFilterForm
from project.apps.tecpap.models import Manufacturer, Model, Type
from project.apps.partner.models import StockRecord
from project.apps.catalogue.paginators import CachedPaginator

from .forms import ContactVendorForm, PriceOfferForm
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger('apps.catalogue')

Product = get_model('catalogue', 'Product')
Category = get_model('catalogue', 'Category')
StockRecord = get_model('partner', 'StockRecord')
ProductImage = get_model('catalogue', 'ProductImage')
TecpapAttributeGroup = get_model('catalogue', 'TecpapAttributeGroup')
TecpapManufacturer = get_model('tecpap', 'Manufacturer')
TecpapModel = get_model('tecpap', 'Model')
TecpapType = get_model('tecpap', 'Type')


class GetProductExtraAttributes(View):
    def get(self, request, *args, **kwargs):
        category_ids = request.GET.getlist('category_id[]', [])

        att_ids = []
        if any(category_ids):
            for category_id in category_ids:
                if category_id:
                    try:
                        category = Category.objects.get(id=category_id)
                    except Category.DoesNotExist:
                        pass
                    else:
                        att_ids += [
                            {'id': i.product_extra_attribute_id, 'name': i.product_extra_attribute.name}
                            for i in category.productextraattributecategory_set.all()
                        ]

        return HttpResponse(json.dumps(att_ids), content_type='application/json')


class GetShippingTemplatesView(View):
    def get(self, request, *args, **kwargs):
        weight = request.GET.get('weight', '0').replace(',', '.')
        height = request.GET.get('height', '0').replace(',', '.')
        width = request.GET.get('width', '0').replace(',', '.')
        length = request.GET.get('length', '0').replace(',', '.')

        weight = weight if weight else '0'
        height = height if height else '0'
        width = width if width else '0'
        length = length if length else '0'

        shipping_templates = self.request.user.account.get_shipping_templates(weight, height, width, length)

        return HttpResponse(json.dumps(shipping_templates), content_type='application/json')


class ProductRedirectView(View):
    def get(self, request, *args, **kwargs):
        old_slug, old_prodct_id = kwargs['slug'].replace('/', '-').split('_')
        old_slug_parts = old_slug.split('-')
        old_slug_parts.reverse()
        search_keyword = ''
        for slug_part in old_slug_parts:
            if len(slug_part) > 10:
                search_keyword = slug_part

        return HttpResponsePermanentRedirect('{}?q={}'.format(reverse('search:search'), search_keyword))


class ManufacturerModelsListView(View):
    def get(self, request, *args, **kwargs):
        models = Manufacturer.get_models(request)
        return HttpResponse(json.dumps(models), content_type='application/json')


class ModelTypesListView(View):
    def get(self, request, *args, **kwargs):
        types = Model.get_types(request)
        return HttpResponse(json.dumps(types), content_type='application/json')


# Frontend versions with more validation
class ManufacturerModelsListViewStorefront(View):
    def get(self, request, *args, **kwargs):
        # Get manufacturer ID from request
        manufacturer_id = request.GET.get('manufacturer')
        if not manufacturer_id:
            manufacturer_id = request.GET.get('manufacturers')
        
        if not manufacturer_id:
            return HttpResponse(json.dumps([]), content_type='application/json')

        # Get current language for language-specific caching
        current_language = request.LANGUAGE_CODE

        # Create a cache key that includes manufacturer ID and language
        cache_key = f'manufacturer_models_frontend_{manufacturer_id}_{current_language}'

        # Try to get data from cache first
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return HttpResponse(cached_data, content_type='application/json')

        # If not in cache, perform the database queries
        # Get available product IDs with stock
        # Note: is_available already means stock > 0 (set by Product.check_availability() cron job)
        available_product_ids = Product.objects.filter(
            active=True, 
            is_available=True
        ).values_list('id', flat=True)
        
        # Get model IDs for products in stock with the selected manufacturer
        model_ids = Product.objects.filter(
            id__in=available_product_ids,
            tecpap_attributes__manufacturer__id=manufacturer_id
        ).values_list('tecpap_attributes__model__id', flat=True).distinct()
        
        # Get models
        models = Model.objects.filter(id__in=model_ids, active=True).order_by('name')
        
        # Format models as expected by the frontend
        model_list = [[str(m.id), str(m)] for m in models]

        # Convert to JSON
        json_data = json.dumps(model_list)

        # Store in cache for 1 hour (3600 seconds)
        cache.set(cache_key, json_data, 3600)

        return HttpResponse(json_data, content_type='application/json')


class ModelTypesListViewStorefront(View):
    def get(self, request, *args, **kwargs):
        # Get model IDs from request
        model_ids_param = request.GET.get('models', '')
        
        # Handle multiple models either as comma-separated string or as list
        if not model_ids_param:
            return HttpResponse(json.dumps([]), content_type='application/json')
        
        # Parse model IDs
        if ',' in model_ids_param:
            model_ids = model_ids_param.split(',')
        else:
            model_ids = request.GET.getlist('models', [])
            
        if not model_ids:
            return HttpResponse(json.dumps([]), content_type='application/json')

        # Get current language for language-specific caching
        current_language = request.LANGUAGE_CODE

        # Create a cache key that includes model IDs and language
        # Sort model_ids to ensure consistent cache key regardless of order
        sorted_model_ids = sorted(model_ids)
        model_ids_str = '_'.join(sorted_model_ids)
        cache_key = f'model_types_frontend_{model_ids_str}_{current_language}'

        # Try to get data from cache first
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return HttpResponse(cached_data, content_type='application/json')

        # Get available product IDs with stock
        # Note: is_available already means stock > 0 (set by Product.check_availability() cron job)
        available_product_ids = Product.objects.filter(
            active=True, 
            is_available=True
        ).values_list('id', flat=True)
        
        # Get type IDs for products in stock with the selected models
        type_ids = Product.objects.filter(
            id__in=available_product_ids,
            tecpap_attributes__model__id__in=model_ids
        ).values_list('tecpap_attributes__typ__id', flat=True).distinct()
        
        # Get types
        types = Type.objects.filter(id__in=type_ids, active=True).order_by('name')
        
        # Format types as expected by the frontend
        type_list = [[str(t.id), str(t)] for t in types]

        # Convert to JSON
        json_data = json.dumps(type_list)

        # Store in cache for 1 hour (3600 seconds)
        cache.set(cache_key, json_data, 3600)

        return HttpResponse(json_data, content_type='application/json')


class TypeEnginesListView(View):
    def get(self, request, *args, **kwargs):
        engines = Type.get_engines(request)
        return HttpResponse(json.dumps(engines), content_type='application/json')


class ContactVendorView(FormView):
    form_class = ContactVendorForm
    template_name = 'oscar/catalogue/contact_form.html'

    def form_valid(self, form):
        name = form.cleaned_data['name']
        email = form.cleaned_data['email']
        message = form.cleaned_data['message']
        product = Product.objects.get(id=self.kwargs['pk'])
        product_url = self.request.build_absolute_uri(product.get_absolute_url())

        message = 'Name: {}\nEmail: {}\nProduct: {}\nMessage:\n\n{}'.format(name, email, product_url, message)

        if self.request.branch.branch == 'eu':
            if product.subowner:
                message_to = [product.subowner.email, product.owner.email]
            else:
                message_to = [product.owner.email]
            message_from = settings.OSCAR_FROM_EMAIL
        else:
            message_to = [product.owner.email, self.request.branch.contact_email, '<EMAIL>']
            message_from = settings.BRANCH_SETTINGS[self.request.branch.branch]['email_from']
        send_mail(
            'Message from customer (product commercial ID %s)' % product.commercial_id,
            message,
            message_from,
            message_to,
            fail_silently=False,
        )
        messages.success(self.request, str(_('Your message sent successfully')))
        return HttpResponseRedirect(self.request.path)


class PriceOfferView(FormView):
    form_class = PriceOfferForm
    template_name = 'oscar/catalogue/price_offer_form.html'

    def form_valid(self, form):
        name = form.cleaned_data['name']
        email = form.cleaned_data['email']
        comment = form.cleaned_data['comment']
        price = form.cleaned_data['price']
        product = Product.objects.get(id=self.kwargs['pk'])
        product_url = self.request.build_absolute_uri(product.get_absolute_url())
        user_currency = self.request.session.get('user_currency', self.request.basket.get_default_user_currency())

        price_with_currency = '{} {}'.format(price, user_currency['currency'])
        try:
            _price_d = D(price)
        except ValueError:
            pass
        else:
            if user_currency['currency'] != 'EUR':
                price_eur = (_price_d / D(user_currency['rate'])).quantize(D('0.00'))
                price_with_currency = '{} ({} EUR)'.format(price_with_currency, price_eur)

        message = 'Name: {}\nEmail: {}\nProduct: {}\nOffered price: {}\nComment:\n\n{}'.format(
            name, email, product_url, price_with_currency, comment
        )

        if self.request.branch.branch == 'eu':
            if product.subowner:
                message_to = [product.subowner.email, product.owner.email]
            else:
                message_to = [product.owner.email]
            message_from = settings.OSCAR_FROM_EMAIL
        else:
            message_to = [product.owner.email, self.request.branch.contact_email, '<EMAIL>']
            message_from = settings.BRANCH_SETTINGS[self.request.branch.branch]['email_from']
        message_to.append('<EMAIL>')
        send_mail(
            'Price offer from customer (product commercial ID %s)' % product.commercial_id,
            message,
            message_from,
            message_to,
            fail_silently=False,
        )
        messages.success(self.request, str(_('Your offer sent successfully')))
        return HttpResponseRedirect(self.request.path)


class ProductDetailView(SearchMixin, views.ProductDetailView):
    def get_etag(self, request, product, stock=None):
        """Generate ETag based on product's last modification, stock info, price and language"""
        try:
            last_modified = product.date_updated.strftime('%Y-%m-%d %H:%M:%S')
            if stock is None:
                stock = product.stockrecords.first()
            stock_modified = stock.date_updated.strftime('%Y-%m-%d %H:%M:%S') if stock else ''
            price = str(stock.price) if stock else '0.00'
            content = f"{last_modified}_{stock_modified}_{price}_{request.LANGUAGE_CODE}"
            return f'"{hashlib.md5(content.encode()).hexdigest()}"'
        except AttributeError:
            return None

    def get_last_modified(self, product, stock=None):
        """Get the last modified timestamp for the product"""
        try:
            last_modified = product.date_updated
            if stock is None:
                stock = product.stockrecords.first()
            if stock and stock.date_updated:
                last_modified = max(last_modified, stock.date_updated)
            return last_modified
        except AttributeError:
            return None

    def get(self, request, *args, **kwargs):
        # First get the product using Oscar's standard logic
        self.object = super().get_object()

        # Always check if we need to redirect to slugged URL first
        current_url = request.path
        correct_url = self.object.get_absolute_url()

        if current_url != correct_url:
            return HttpResponsePermanentRedirect(correct_url)

        # Get stock once to avoid repeated queries
        stock = self.object.stockrecords.first()

        # Then check availability
        try:
            stock_is_sufficient = stock.num_in_stock > 0 if stock else False
        except (AttributeError, TypeError):
            stock_is_sufficient = False

        if not self.object.is_available or not stock_is_sufficient:
            response = TemplateResponse(
                request=request,
                template='oscar/catalogue/410.html',
                context={},
                status=410
            )
            return response

        # Get ETag and Last-Modified values using the cached stock
        etag = self.get_etag(request, self.object, stock)
        last_modified = self.get_last_modified(self.object, stock)

        # Get cache control headers from request
        cache_control = request.META.get('HTTP_CACHE_CONTROL', '').lower()
        pragma = request.META.get('HTTP_PRAGMA', '').lower()
        if_none_match = request.META.get('HTTP_IF_NONE_MATCH')
        if_modified_since = request.META.get('HTTP_IF_MODIFIED_SINCE')

        # Check if we should return 304 Not Modified
        should_return_304 = False

        # Simple ETag comparison
        if etag and if_none_match and if_none_match == etag:
            should_return_304 = True

        if last_modified and if_modified_since:
            try:
                if_modified_since = datetime.datetime.strptime(
                    if_modified_since, "%a, %d %b %Y %H:%M:%S GMT"
                ).replace(tzinfo=timezone.utc)
                if last_modified <= if_modified_since:
                    should_return_304 = True
            except ValueError:
                pass

        # Return 304 if content hasn't changed and client isn't explicitly requesting fresh content
        force_refresh = 'no-cache' in cache_control or 'no-store' in cache_control or 'no-cache' in pragma
        if should_return_304 and not force_refresh:
            response = HttpResponse(status=304)
            response['ETag'] = etag
            response['Last-Modified'] = last_modified.strftime("%a, %d %b %Y %H:%M:%S GMT")
            response['Cache-Control'] = 'no-cache'
            return response

        # Get and prepare the response
        context = self.get_context_data(object=self.object)
        response = self.render_to_response(context)

        # Add the ETag and Last-Modified headers
        if etag:
            response['ETag'] = etag
        if last_modified:
            response['Last-Modified'] = last_modified.strftime("%a, %d %b %Y %H:%M:%S GMT")

        # Add Cache-Control and Vary headers
        response['Cache-Control'] = 'no-cache'
        response['Vary'] = 'Accept-Encoding, Cookie'

        return response

    def get_context_data(self, **kwargs):
        context = super(ProductDetailView, self).get_context_data(**kwargs)
        context['shipping_form'] = ShippingForm(request=self.request)

        # Pre-calculate frequently used product data to avoid repeated DB queries
        product = self.object

        # Get tecpap attributes once and cache the results
        tecpap_attrs = list(product.tecpap_attributes.select_related('manufacturer').prefetch_related('model', 'typ').all())

        # Pre-calculate main attributes to avoid repeated queries in template
        if tecpap_attrs:
            first_attr = tecpap_attrs[0]
            context['main_manufacturer'] = first_attr.manufacturer.brand if first_attr.manufacturer else ""
            context['main_year'] = first_attr.year

            # Get first model name and clean it
            models = list(first_attr.model.all())
            if models:
                model_name = models[0].name
                context['main_model_cleaned'] = model_name.partition('(')[0].strip() if model_name else ""
                context['main_model'] = models[0]
            else:
                context['main_model_cleaned'] = ""
                context['main_model'] = None

            # Get first type
            types = list(first_attr.typ.all())
            context['main_type'] = types[0] if types else None

            # Store the first attribute for template use
            context['main_tecpap_attr'] = first_attr
        else:
            context['main_manufacturer'] = ""
            context['main_year'] = ""
            context['main_model_cleaned'] = ""
            context['main_model'] = None
            context['main_type'] = None
            context['main_tecpap_attr'] = None

        # Get main category once
        categories = list(product.categories.all())
        if categories:
            context['main_category'] = categories[0].name.replace("\n", " ").replace("\r", "")
        else:
            context['main_category'] = ""

        # Pre-calculate DPD cash countries to avoid repeated queries
        context['dpd_cash_countries'] = self._get_dpd_cash_countries(product)

        # Pre-calculate purchase info once
        if product.is_parent:
            context['purchase_info'] = self.request.strategy.fetch_for_parent(product)
        else:
            context['purchase_info'] = self.request.strategy.fetch_for_product(product)

        # Pre-calculate discount info once
        context['discount_info'] = self.request.strategy.discount_for_product(product)

        # Pre-fetch images to avoid repeated queries
        context['product_images'] = list(product.images.all())

        # Pre-fetch first category for breadcrumbs and its ancestors
        main_category_obj = product.categories.first()
        context['main_category_obj'] = main_category_obj
        if main_category_obj:
            context['category_ancestors'] = list(main_category_obj.get_ancestors())
        else:
            context['category_ancestors'] = []

        # Pre-fetch eBay advertisement to avoid template tag query
        context['ebay_ad'] = None
        if product.pap_product_id:
            try:
                from project.apps.pap.models import VehiclePartAdvertisment
                from django.conf import settings

                context['ebay_ad'] = VehiclePartAdvertisment.objects.filter(
                    vehicle_part_id=product.pap_product_id,
                    shop=settings.SHOP_EBAY,
                    ebay_listing_id__isnull=False,
                    active=True
                ).first()
            except (ImportError, AttributeError):
                pass

        return context

    def _get_dpd_cash_countries(self, product):
        """Calculate DPD cash countries once to avoid repeated queries"""
        dpd_cash_countries = []
        try:
            from project.apps.order.models import PaymentMethod
            from project.accounts.models import Account, Branch

            dpd_cash_payment_method = PaymentMethod.objects.get(code="dpd_cash")
        except PaymentMethod.DoesNotExist:
            return dpd_cash_countries

        if dpd_cash_payment_method:
            try:
                Account.objects.get(user=product.owner, payment_methods=dpd_cash_payment_method)
                dpd_cash_countries = ["LT", "LV", "EE"]
            except Account.DoesNotExist:
                pass

            dpd_cash_branch_countries = list(
                Branch.objects.filter(payment_methods=dpd_cash_payment_method).values_list("country", flat=True)
            )
            dpd_cash_countries += dpd_cash_branch_countries

        return dpd_cash_countries

    def get_queryset(self):
        """
        Return a queryset of products that can be viewed.
        Optimized with select_related and prefetch_related to reduce
        database queries on the product detail page.
        """
        qs = super().get_queryset().select_related(
            'product_class',
            'owner__account',
            'subowner__account',
        ).prefetch_related(
            Prefetch('stockrecords',
                     queryset=StockRecord.objects.select_related('partner')),
            Prefetch('images',
                     queryset=ProductImage.objects.order_by('display_order')),
            'categories',
            'extra_attributes',
            Prefetch('tecpap_attributes',
                     queryset=TecpapAttributeGroup.objects.order_by('id').select_related(
                         'manufacturer'
                     ).prefetch_related(
                         Prefetch('model', queryset=TecpapModel.objects.order_by('id')),
                         Prefetch('typ', queryset=TecpapType.objects.order_by('id').select_related('fuel'))
                     )
            ),
        )
        return qs


class ProductCategoryView(ListView):
    """
    Browse products in a given category
    """
    context_object_name = "products"
    template_name = 'oscar/catalogue/browse.html'
    paginator_class = CachedPaginator

    @property
    def paginate_by(self):
        """Get pagination size from request parameters or default to 20"""
        per_page = self.request.GET.get('per_page', '20')
        try:
            return int(per_page) if per_page in ['20', '50', '100'] else 20
        except (ValueError, TypeError):
            return 20

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.manufacturer_catalogue = False
        self.manufacturers = []
        self.category = None
        self.categories = None
        self.selected_category_ids = []
        self.is_htmx_request = False
        self.active_filters = {}
        self._cached_category_data = None
        self.selected_models = []
        self.selected_types = []
        self.year_from = ''
        self.year_to = ''
        self.price_from = ''
        self.price_to = ''

    def dispatch(self, request, *args, **kwargs):
        # Check if this is an HTMX request
        self.is_htmx_request = request.headers.get('HX-Request') == 'true'

        # Get selected category IDs from query parameters
        self.selected_category_ids = [int(cat_id) for cat_id in request.GET.getlist('category_ids', []) if cat_id.isdigit()]
        if self.selected_category_ids:
            self.active_filters['categories'] = self.selected_category_ids

        # Get manufacturer from query params (for unified filtering)
        manufacturer_slug = request.GET.get('manufacturer')
        if manufacturer_slug:
            self.manufacturer_catalogue = True
            try:
                manufacturer = Manufacturer.objects.get(slug=manufacturer_slug)
                self.manufacturers = [manufacturer]
                self.active_filters['manufacturer'] = manufacturer
            except Manufacturer.DoesNotExist:
                pass

        # Get selected models and types from query parameters
        self.selected_models = request.GET.getlist('models', [])
        if self.selected_models:
            self.active_filters['models'] = self.selected_models

        self.selected_types = request.GET.getlist('types', [])
        if self.selected_types:
            self.active_filters['types'] = self.selected_types

        # Get selected extra_attributes from query parameters
        self.selected_extra_attributes = request.GET.getlist('extra_attributes', [])
        if self.selected_extra_attributes:
            self.active_filters['extra_attributes'] = self.selected_extra_attributes

        # Get year range filters
        self.year_from = request.GET.get('year_from', '')
        self.year_to = request.GET.get('year_to', '')
        if self.year_from or self.year_to:
            self.active_filters['year_range'] = {
                'from': self.year_from,
                'to': self.year_to
            }

        # Get price range filters
        self.price_from = request.GET.get('price_from', '')
        self.price_to = request.GET.get('price_to', '')
        if self.price_from or self.price_to:
            self.active_filters['price_range'] = {
                'from': self.price_from,
                'to': self.price_to
            }

        # Get category and manufacturer info
        self.get_object()

        # Explicitly set these attributes on the request to prevent redundant queries
        # This works better than context variables since context processors run after the view
        request.disable_promotions = True  # Prevents promotions_pagepromotion queries
        request.disable_rawhtml = True     # Prevents promotions_rawhtml queries
        request.disable_reviews = True     # Prevents reviews_orderreview queries

        # Call parent dispatch
        return super(ProductCategoryView, self).dispatch(request, *args, **kwargs)

    def get_object(self):
        """Get the category and manufacturer from the URL"""
        # First check if we have specific category_ids in the URL parameters
        if self.selected_category_ids and not self.category:
            try:
                # Use the first selected category as the main category
                self.category = self._get_category_data().get(id=self.selected_category_ids[0])
            except Category.DoesNotExist:
                pass

        # If no category found yet, use URL parameters
        if not self.category and 'pk' in self.kwargs:
            self.category = get_object_or_404(Category, pk=self.kwargs['pk'])
            # Ensure the URL category is always in active_filters for filtering
            if 'categories' not in self.active_filters:
                self.active_filters['categories'] = [self.category.id]
            elif self.category.id not in self.active_filters['categories']:
                self.active_filters['categories'].append(self.category.id)

        # If still no category, use root category as default
        if not self.category:
            self.category = Category.objects.filter(depth=1).first()

        # Load categories as they are essential
        self.categories = self.get_categories()

        # Check if this is a manufacturer catalogue from URL parameters
        if 'manufacturer_slug' in self.kwargs and not self.manufacturer_catalogue:
            self.manufacturer_catalogue = True
            manufacturer_slug = self.kwargs['manufacturer_slug']

            try:
                manufacturer = Manufacturer.objects.get(slug=manufacturer_slug)
                self.manufacturers = [manufacturer]
                self.active_filters['manufacturer'] = manufacturer
            except Manufacturer.DoesNotExist:
                pass

    def _get_category_data(self):
        """Get category data with in-memory caching to prevent duplicate queries"""
        if self._cached_category_data is None:
            if self.selected_category_ids:
                self._cached_category_data = list(Category.objects.filter(id__in=self.selected_category_ids))
            else:
                self._cached_category_data = []
        return self._cached_category_data

    def get_categories(self):
        """
        Get category filter using path-based lookup instead of get_descendants_and_self()
        This is more efficient as it uses a single query with path-based filtering
        """
        # Get the current category's path
        category_path = self.category.path
        # Use path lookup to get all descendants efficiently
        return Category.objects.filter(path__startswith=category_path)

    def get_ordering_options(self):
        """
        Return available ordering options
        """
        return {
            'price_asc': ('stockrecords__price', 'id'),
            'price_desc': ('-stockrecords__price', 'id'),
            'date_asc': ('date_created', 'id'),
            'date_desc': ('-date_created', 'id'),
            'title_asc': ('categories__name', 'id'),
            'title_desc': ('-categories__name', 'id'),
        }

    def get_ordering(self):
        """
        Get the ordering tuple based on request parameters
        """
        ordering_param = self.request.GET.get('sort_by', 'date_desc')
        return self.get_ordering_options().get(ordering_param, ('-date_created', 'id'))

    def get_base_queryset(self):
        qs = Product.objects.filter(active=True, is_available=True)
        if 'manufacturer' in self.active_filters:
            qs = qs.filter(tecpap_attributes__manufacturer=self.active_filters['manufacturer'])
        qs = self._apply_category_filters(qs)
        if 'models' in self.active_filters:
            try:
                model_ids = [int(model_id) for model_id in self.active_filters['models'] if model_id.isdigit()]
                if model_ids:
                    qs = qs.filter(tecpap_attributes__model__id__in=model_ids)
            except (ValueError, TypeError):
                pass
        if 'types' in self.active_filters:
            try:
                type_ids = [int(type_id) for type_id in self.active_filters['types'] if type_id.isdigit()]
                if type_ids:
                    qs = qs.filter(tecpap_attributes__typ__id__in=type_ids)
            except (ValueError, TypeError):
                pass
        # Add distinct() to prevent duplicates when joining with related models
        return qs.distinct()

    def get_queryset(self):
        base_qs = self.get_base_queryset()
        self._main_base_qs = base_qs  # Save for use in context
        final_qs = base_qs

        # Apply extra attributes filter
        if 'extra_attributes' in self.active_filters:
            try:
                extra_attr_ids = [int(attr_id) for attr_id in self.active_filters['extra_attributes'] if attr_id.isdigit()]
                if extra_attr_ids:
                    final_qs = final_qs.filter(extra_attributes__id__in=extra_attr_ids).distinct()
            except (ValueError, TypeError):
                pass

        # Apply year range filter as a final filter
        if 'year_range' in self.active_filters:
            year_range = self.active_filters['year_range']
            if year_range['from'] and year_range['from'].isdigit() and len(year_range['from']) == 4:
                final_qs = final_qs.filter(tecpap_attributes__year__gte=year_range['from']).distinct()
            if year_range['to'] and year_range['to'].isdigit() and len(year_range['to']) == 4:
                final_qs = final_qs.filter(tecpap_attributes__year__lte=year_range['to']).distinct()

        # Apply price range filter as a final filter
        if 'price_range' in self.active_filters:
            price_range = self.active_filters['price_range']
            try:
                if price_range['from'] and float(price_range['from']) > 0:
                    final_qs = final_qs.filter(stockrecords__price__gte=float(price_range['from'])).distinct()
                if price_range['to'] and float(price_range['to']) > 0:
                    final_qs = final_qs.filter(stockrecords__price__lte=float(price_range['to'])).distinct()
            except (ValueError, TypeError):
                pass

        # Import necessary modules for product fetching
        from django.db.models import Prefetch
        from project.apps.partner.models import StockRecord
        stockrecord_prefetch = Prefetch(
            'stockrecords',
            queryset=StockRecord.objects.all(),
            to_attr='_all_stockrecords'
        )
        ordering = self.get_ordering()
        # Create a custom iterator that will fetch full product data only for the paginated slice
        final_qs = final_qs.order_by(*ordering)
        class ProductQuerySet:
            def __init__(self, base_qs):
                self.base_qs = base_qs
                self._count = None
            def __len__(self):
                # Note: This count is cached by CachedPaginator, so the database query
                # is not executed on every request, only on cache misses or filter changes.
                if self._count is None:
                    self._count = self.base_qs.values('id').distinct().count()
                return self._count
            def __getitem__(self, slice_obj):
                if isinstance(slice_obj, slice):
                    product_ids = list(self.base_qs.values_list('id', flat=True).distinct()[slice_obj])
                    stockrecord_prefetch_with_partner = Prefetch(
                        'stockrecords',
                        queryset=StockRecord.objects.select_related('partner').all(),
                        to_attr='_all_stockrecords'
                    )
                    qs = Product.objects.filter(id__in=product_ids).select_related(
                        'product_class',
                    ).prefetch_related(
                        'images',
                        stockrecord_prefetch_with_partner,
                        'tecpap_attributes__manufacturer',
                        'tecpap_attributes__model',
                        'tecpap_attributes__typ',
                        'tecpap_attributes__typ__engines',
                        'tecpap_attributes__typ__engines__eng',
                        'tecpap_attributes__typ__fuel',
                        'extra_attributes',
                        'categories',
                    ).only(
                        'id',
                        'title',
                        'slug',
                        'date_created',
                        'product_class_id',
                        'structure',
                        'original_code',
                        'condition',
                    )

                    # Preserve the ordering from base_qs and fetch PurchaseInfo
                    product_dict = {p.id: p for p in qs}
                    # Assign main_category for each product
                    for p in product_dict.values():
                        p.main_category = p.categories.first() if hasattr(p, 'categories') and p.categories.exists() else None
                        # Assign main_engine for each typ in tecpap_attributes
                        for attr in getattr(p, 'tecpap_attributes', []).all():
                            for typ in getattr(attr, 'typ', []).all():
                                typ.main_engine = next((e for e in typ.engines.all() if e.active), None)
                    results = []
                    # Get strategy instance once
                    from oscar.core.loading import get_class
                    Selector = get_class('partner.strategy', 'Selector')
                    strategy = Selector().strategy()
                    for product_id in product_ids:
                        if product_id in product_dict:
                            product = product_dict[product_id]
                            # Fetch purchase info using the strategy
                            # This leverages the prefetched _all_stockrecords via select_stockrecord
                            if product.is_parent:
                                product.purchase_info = strategy.fetch_for_parent(product)
                            else:
                                product.purchase_info = strategy.fetch_for_product(product)
                            results.append(product)
                    return results
                else:
                    # Single item access - should not happen in pagination
                    # Fetching PurchaseInfo for single item if needed (though unlikely for pagination)
                    product = list(self.base_qs[slice_obj:slice_obj+1])[0]
                    if product:
                        from oscar.core.loading import get_class
                        Selector = get_class('partner.strategy', 'Selector')
                        strategy = Selector().strategy()
                        if product.is_parent:
                             product.purchase_info = strategy.fetch_for_parent(product)
                        else:
                             product.purchase_info = strategy.fetch_for_product(product)
                    return product
        return ProductQuerySet(final_qs)

    def _apply_category_filters(self, base_qs):
        """
        Apply category filters to the product queryset using tree-based path lookups.
        This handles hierarchical category filtering efficiently with django-treebeard.

        Args:
            base_qs: The base queryset of products to filter.

        Returns:
            Filtered queryset based on selected categories or main category.
        """
        from django.db.models import Exists, OuterRef, Q

        if 'categories' in self.active_filters and self.active_filters['categories']:
            # When we have selected categories, use those for filtering
            categories_to_filter = self._get_category_data()
            if not categories_to_filter and self.category:
                # Fallback to main category if no cached data
                categories_to_filter = [self.category]

            # Build a complex OR query for each selected category's path to include descendants
            # This ensures products in any selected category or its subcategories are included
            path_q = Q()
            for cat in categories_to_filter:
                path_q |= Q(path__startswith=cat.path)

            # Use EXISTS subquery for performance to check if a product belongs to any matching category
            # OuterRef('pk') refers to the product ID in the outer query, avoiding costly JOINs
            category_exists = Category.objects.filter(
                path_q,
                productcategory__product=OuterRef('pk')
            ).values('id')[:1]

            return base_qs.filter(Exists(category_exists))
        elif self.category:
            # Default behavior - filter by the main category and its descendants
            category_path = self.category.path
            category_exists = Category.objects.filter(
                path__startswith=category_path,
                productcategory__product=OuterRef('pk')
            ).values('id')[:1]

            return base_qs.filter(Exists(category_exists))

        return base_qs

    def get_context_data(self, **kwargs):
        from django.urls import reverse
        from urllib.parse import urlencode
        
        context = super(ProductCategoryView, self).get_context_data(**kwargs)

        # Generate category tree for full page requests
        # For HTMX requests, we'll generate it later if needed for OOB updates
        if not self.is_htmx_request:
            context['category_tree'] = self.get_category_tree()

        # ---
        # Category counts: filtered by manufacturer/model/type, NOT by category
        # ---
        base_qs_for_category_counts = Product.objects.filter(active=True, is_available=True)
        if 'manufacturer' in self.active_filters:
            base_qs_for_category_counts = base_qs_for_category_counts.filter(tecpap_attributes__manufacturer=self.active_filters['manufacturer'])
        if 'models' in self.active_filters:
            try:
                model_ids = [int(model_id) for model_id in self.active_filters['models'] if model_id.isdigit()]
                if model_ids:
                    base_qs_for_category_counts = base_qs_for_category_counts.filter(tecpap_attributes__model__id__in=model_ids)
            except (ValueError, TypeError):
                pass
        if 'types' in self.active_filters:
            try:
                type_ids = [int(type_id) for type_id in self.active_filters['types'] if type_id.isdigit()]
                if type_ids:
                    base_qs_for_category_counts = base_qs_for_category_counts.filter(tecpap_attributes__typ__id__in=type_ids)
            except (ValueError, TypeError):
                pass
        # Add distinct to prevent duplicates
        base_qs_for_category_counts = base_qs_for_category_counts.distinct()
        # Do NOT filter by category for category counts!

        # ---
        # Attribute counts: filtered by manufacturer/model/type AND categories (if any selected)
        # ---
        base_qs_for_attribute_counts = base_qs_for_category_counts
        if self.selected_category_ids:
            categories_to_filter = list(Category.objects.filter(id__in=self.selected_category_ids))
            if categories_to_filter:
                path_q = Q()
                for cat in categories_to_filter:
                    path_q |= Q(path__startswith=cat.path)
                category_exists = Category.objects.filter(
                    path_q,
                    productcategory__product=OuterRef('pk')
                ).values('id')[:1]
                base_qs_for_attribute_counts = base_qs_for_attribute_counts.filter(Exists(category_exists)).distinct()
        elif self.category:
            # If no explicit selected_category_ids, but a main category is set (e.g. via URL)
            category_path = self.category.path
            category_exists = Category.objects.filter(
                path__startswith=category_path,
                productcategory__product=OuterRef('pk')
            ).values('id')[:1]
            base_qs_for_attribute_counts = base_qs_for_attribute_counts.filter(Exists(category_exists)).distinct()

        # Build deterministic cache key from all relevant filters
        manufacturer_id = str(self.manufacturers[0].id) if self.manufacturers else 'none'
        model_ids = '_'.join(sorted(self.selected_models)) if self.selected_models else 'none'
        type_ids = '_'.join(sorted(self.selected_types)) if self.selected_types else 'none'
        category_ids = '_'.join(str(cid) for cid in sorted(self.selected_category_ids)) if self.selected_category_ids else (
            str(self.category.id) if self.category else 'none')
        cache_key_cat = f'category_products_count_all_{manufacturer_id}_{model_ids}_{type_ids}'
        cache_key_attr = f'attribute_counts_{category_ids}_{manufacturer_id}_{model_ids}_{type_ids}'
        category_counts = cache.get(cache_key_cat)
        attribute_counts = cache.get(cache_key_attr)
        if category_counts is None:
            category_counts = get_product_counts_by_category_path(base_qs_for_category_counts)
            cache.set(cache_key_cat, category_counts, 900)
        if attribute_counts is None:
            attribute_counts = get_extra_attribute_counts(base_qs_for_attribute_counts)
            cache.set(cache_key_attr, attribute_counts, 900)
        context['category_product_counts'] = category_counts
        if not self.selected_category_ids and 'categories' in self.active_filters:
            self.selected_category_ids = self.active_filters['categories']

        # Serialize filter data to JSON - keeping individual JSON fields for backward compatibility
        context['initial_categories_json'] = json.dumps(self.selected_category_ids)
        context['initial_manufacturer_json'] = json.dumps(self.manufacturers[0].slug if self.manufacturer_catalogue and self.manufacturers else '')
        context['initial_models_json'] = json.dumps(self.selected_models)
        context['initial_types_json'] = json.dumps(self.selected_types)

        # Add year and price range filters to context
        context['year_from'] = self.year_from
        context['year_to'] = self.year_to
        context['price_from'] = self.price_from
        context['price_to'] = self.price_to

        # Simple category IDs extraction
        if context.get('category_tree'):
            context['category_ids_json'] = json.dumps([cat['id'] for cat in context['category_tree']])
        else:
            context['category_ids_json'] = json.dumps([])
        context['selected_category_ids'] = self.selected_category_ids
        context['catalogue_base_url'] = reverse('catalogue:index')
        context['category'] = self.category
        context['manufacturer'] = self.manufacturers[0] if self.manufacturer_catalogue and self.manufacturers else None
        context['selected_models'] = self.selected_models
        context['selected_types'] = self.selected_types
        
        # Get manufacturers with a single query using Exists for efficiency
        cache_key = 'active_manufacturers_with_products'
        cached_manufacturers = cache.get(cache_key)
        if cached_manufacturers is not None:
            context['manufacturers'] = cached_manufacturers
        else:
            active_products_subquery = Product.objects.filter(
                tecpap_attributes__manufacturer=OuterRef('pk'),
                active=True,
                is_available=True
            )
            context['manufacturers'] = Manufacturer.objects.filter(
                active=True
            ).annotate(
                has_active_products=Exists(active_products_subquery)
            ).filter(
                has_active_products=True
            ).order_by('brand')
            cache.set(cache_key, context['manufacturers'], 7200)  # Cache for 2 hours
        
        # Generate manufacturers JSON directly
        manufacturers_json = json.dumps([{
            'id': m.id, 
            'slug': m.slug, 
            'brand': m.brand
        } for m in context['manufacturers']])
        
        context['manufacturers_json'] = manufacturers_json
        context['is_paginated'] = context['paginator'].num_pages > 1
        context['per_page_options'] = [20, 50, 100]
        context['current_per_page'] = self.paginate_by
        if context['is_paginated']:
            context['page'] = context['page_obj']
            context['min_page'] = max(1, context['page_obj'].number - 4)
            context['max_page'] = min(context['paginator'].num_pages, context['page_obj'].number + 4)
            
            # Build standardized pagination URLs using catalogue:index as base
            # Start with a fresh query params dict
            query_params = {}
            
            # Add parameters that can have multiple values
            if self.selected_category_ids:
                query_params['category_ids'] = self.selected_category_ids
            elif self.category and 'pk' in self.kwargs:
                query_params['category_ids'] = [self.kwargs['pk']]
            
            # Add models (can have multiple values)
            if self.selected_models:
                query_params['models'] = self.selected_models
                
            # Add types (can have multiple values)
            if self.selected_types:
                query_params['types'] = self.selected_types
                
            # Add extra attributes (can have multiple values)
            if self.selected_extra_attributes:
                query_params['extra_attributes'] = self.selected_extra_attributes
                
            # Add manufacturer if available
            if self.manufacturer_catalogue and self.manufacturers:
                query_params['manufacturer'] = self.manufacturers[0].slug
                
            # Add all other single-value parameters from the request
            # Skip parameters we've already handled and those we want to exclude
            exclude_params = ['page', 'category_ids', 'models', 'types', 'extra_attributes', 'manufacturer']
            for key, value in self.request.GET.items():
                if key not in exclude_params and value:
                    # Check if this is a multi-value parameter that we missed
                    if key.endswith('[]') or self.request.GET.getlist(key) and len(self.request.GET.getlist(key)) > 1:
                        query_params[key] = self.request.GET.getlist(key)
                    else:
                        query_params[key] = value
            
            # Build the standardized base URL with proper handling of list parameters
            base_url = reverse('catalogue:index')
            if query_params:
                # Use doseq=True to properly handle list parameters
                base_url = f"{base_url}?{urlencode(query_params, doseq=True)}"
                context['page_url'] = f"{base_url}&page="
            else:
                context['page_url'] = f"{base_url}?page="
            
        context['sorting_options'] = [
            {'value': 'price_asc', 'label': _('Price (low to high)')},
            {'value': 'price_desc', 'label': _('Price (high to low)')},
            {'value': 'date_asc', 'label': _('Date (oldest first)')},
            {'value': 'date_desc', 'label': _('Date (newest first)')},
            {'value': 'title_asc', 'label': _('Category (A-Z)')},
            {'value': 'title_desc', 'label': _('Category (Z-A)')},
        ]
        context['current_sorting'] = self.request.GET.get('sort_by', 'date_desc')
        from project.apps.catalogue.models import ProductExtraAttribute
        all_extra_attrs = list(ProductExtraAttribute.objects.all())
        attr_count_map = attribute_counts

        # Create a list of all attributes with counts (0 for those not in the filtered results)
        attribute_counts_list = [
            {
                'id': str(attr.id),
                'name': attr.name,
                'count': attr_count_map.get(attr.id, 0),
                'active': attr_count_map.get(attr.id, 0) > 0
            }
            for attr in all_extra_attrs
        ]

        # Filter to only attributes with products and sort by name
        attributes_with_products = [attr for attr in attribute_counts_list if attr['count'] > 0]

        # Custom sort function: selected attributes first, then sort by letters first (case-insensitive), then numbers and symbols
        def custom_sort_key(attr):
            name = attr['name']
            # First priority: is the attribute selected?
            is_selected = attr['id'] in self.selected_extra_attributes

            # Second priority: is the first character a letter?
            if name and name[0].isalpha():
                return (0 if is_selected else 1, 0, name.lower())  # Selected letters first, then unselected letters, case-insensitive
            else:
                return (0 if is_selected else 1, 1, name.lower())  # Then selected numbers/symbols, then unselected numbers/symbols, case-insensitive

        attributes_with_products.sort(key=custom_sort_key)

        # Check if there are any attributes with products
        attribute_counts_with_products = len(attributes_with_products) > 0

        # Count how many attributes have products
        attributes_with_products_count = len(attributes_with_products)

        # Add to context for both full page and HTMX requests
        context['extra_attributes_json'] = json.dumps(attribute_counts_list)
        context['initial_extra_attributes_json'] = json.dumps(self.selected_extra_attributes)
        context['attribute_counts'] = attributes_with_products  # Only pass attributes with products
        context['attribute_counts_with_products'] = attribute_counts_with_products
        context['selected_extra_attributes'] = self.selected_extra_attributes
        context['is_htmx_request'] = self.is_htmx_request

        # Add count data for HTMX responses
        if self.is_htmx_request:
            # For HTMX requests, we need to ensure we have the category tree
            context['category_tree'] = self.get_category_tree()

            # Get all category IDs from the tree
            all_category_ids = []

            # Function to recursively extract all category IDs from the tree
            def extract_category_ids(categories):
                result = []
                for cat in categories:
                    result.append(cat['id'])
                    if 'subcategories' in cat and cat['subcategories']:
                        result.extend(extract_category_ids(cat['subcategories']))
                return result

            all_category_ids = extract_category_ids(context['category_tree'])
            context['category_ids_json'] = json.dumps(all_category_ids)

            # Create a list of categories with counts for all categories
            category_counts_list = []

            # Function to recursively process all categories and their counts
            def process_categories_with_counts(categories):
                result = []
                for cat in categories:
                    count = category_counts.get(cat['path'], 0)
                    result.append({
                        'id': cat['id'],
                        'path': cat['path'],
                        'count': count
                    })
                    if 'subcategories' in cat and cat['subcategories']:
                        result.extend(process_categories_with_counts(cat['subcategories']))
                return result

            category_counts_list = process_categories_with_counts(context['category_tree'])

            context['category_counts'] = category_counts_list
            context['category_counts_json'] = json.dumps(category_counts)

            # Attribute counts are already prepared above for both full page and HTMX requests

        # SERIALIZE category_tree and category_product_counts for JS
        context['category_tree_json'] = json.dumps(context['category_tree']) if 'category_tree' in context else '[]'
        context['category_product_counts_json'] = json.dumps(context['category_product_counts']) if 'category_product_counts' in context else '{}'

        return context

    def get_category_tree(self):
        """
        Get the category tree for the current language.
        Uses language-specific cache or regenerates it if needed.
        """
        from project.apps.catalogue.utils import get_category_tree
        from django.utils import translation

        # Use the utility function that handles language-specific caching
        return get_category_tree(translation.get_language())

    def get_paginator(self, queryset, per_page, orphans=0, allow_empty_first_page=True, **kwargs):
        """
        Return an instance of the paginator for this view.
        Override to include request and filter context in the CachedPaginator
        """
        filter_context = {
            'manufacturer_ids': [m.id for m in self.manufacturers] if self.manufacturers else [],
            'category_id': self.category.id if self.category else None,
            'selected_category_ids': self.selected_category_ids,
        }

        return self.paginator_class(
            queryset,
            per_page,
            request=self.request,
            filter_context=filter_context,
            orphans=orphans,
            allow_empty_first_page=allow_empty_first_page,
            **kwargs
        )

    def render_to_response(self, context, **response_kwargs):
        """
        Return a response, using the `response_class` for this view, with a
        template rendered with the given context.
        Pass response_kwargs to the constructor of the response class.
        """
        response_kwargs.setdefault('content_type', self.content_type)

        # For HTMX requests, we need to specify that we are
        # returning a partial response to be swapped in
        if self.is_htmx_request:
            # For HTMX requests, render only the content portion using a dedicated template
            from django.shortcuts import render
            from django.template.loader import render_to_string

            # Add HX-Push-Url header to ensure URL updates properly
            current_url = self.request.get_full_path()

            # Render the main products template
            response = render(
                request=self.request,
                template_name='oscar/catalogue/partials/htmx_products.html',
                context=context,
                **response_kwargs
            )

            # Render the extra attributes filter template and append it to the response content
            extra_attributes_html = render_to_string(
                'oscar/catalogue/partials/extraattribute_filter.html',
                context=context,
                request=self.request
            )

            # We don't need to update range filters via HTMX as they are one-way filters

            # For category counts OOB updates, use the category_tree template
            # We'll set category_tree to None to trigger the OOB-only section
            context_for_oob = context.copy()
            context_for_oob['category_tree'] = None  # This triggers the OOB-only section

            category_counts_html = render_to_string(
                'oscar/catalogue/partials/category_tree.html',
                context_for_oob,
                request=self.request
            )

            # Convert response content to string, append both HTML snippets, and update response content
            response_content = response.content.decode('utf-8')
            response_content += extra_attributes_html
            response_content += category_counts_html

            response.content = response_content.encode('utf-8')

            # Add HTMX headers but allow browser caching
            response['HX-Push-Url'] = current_url
            return response
        else:
            response = self.response_class(
                request=self.request,
                template=self.get_template_names(),
                context=context,
                using=self.template_engine,
                **response_kwargs
            )

            # Allow browser caching for normal requests
            return response


class ProductListView(ProductCategoryView):
    def get_object(self):
        if 'pk' in self.kwargs:
            self.category = get_object_or_404(Category, pk=self.kwargs['pk'])
        else:
            # For manufacturer catalog without category, use root category
            self.category = Category.objects.filter(depth=1).first()


class GetShippingPriceView(View):
    """
    Simple JSON API for getting shipping prices
    Similar to vehicle filter model/type endpoints
    """
    def get(self, request, *args, **kwargs):
        try:
            product_id = request.GET.get('product_id')
            country_code = request.GET.get('country')
            shipping_method = request.GET.get('method', 'fixed-price-economy')
            
            if not product_id or not country_code:
                return JsonResponse({'error': 'Missing parameters'}, status=400)
            
            # Get models
            Product = apps.get_model('catalogue', 'Product')
            Country = apps.get_model('address', 'Country')
            Basket = apps.get_model('basket', 'Basket')
            
            try:
                product = Product.objects.get(id=product_id)
                country = Country.objects.get(iso_3166_1_a2=country_code)
            except (Product.DoesNotExist, Country.DoesNotExist):
                return JsonResponse({'error': 'Product or country not found'}, status=404)
            
            # Get shipping price
            shipping_price = product.get_shipping_price(Basket, shipping_method, country)
            
            # Get shipping delay if available
            try:
                shipping_delay = product.get_shipping_delay(country_code)
            except (AttributeError, Exception):
                shipping_delay = None
            
            # Return JSON response
            return JsonResponse({
                'price': float(shipping_price) if shipping_price is not None and shipping_price >= 0 else None,
                'delay': shipping_delay,
                'method': shipping_method,
                'country': country_code,
                'available': shipping_price is not None and shipping_price >= 0
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
